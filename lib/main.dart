import 'package:flutter/material.dart';

import 'app.dart';
import 'flavors.dart';

void main() {
  // Set default flavor to 'gp' if not specified
  const String flavorName = String.fromEnvironment('FLAVOR', defaultValue: 'gp');

  F.appFlavor = Flavor.values.firstWhere(
    (element) => element.name == flavorName,
  );

  // Setup configuration based on flavor
  F.setupConfig();

  runApp(const App());
}
