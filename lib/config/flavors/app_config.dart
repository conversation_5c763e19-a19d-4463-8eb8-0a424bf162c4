
import 'package:gp_h5/flavors.dart';
import 'package:dio/dio.dart';

/// Defines the flavor of the app
enum Flavor {
  gp, // 测试环境
  yhxt, // 沅和信投 (生产-
  bszb, // 宝石资本 (生产-租户站)
  dyzb, // 德盈资本 (生产-租户站)
  rsyp, // 荣顺优配 (生产-演示站)
}

/// Configuration for the app based on flavor

// app_config.dart

class AppConfig {
  final String appName;
  final String siteId;
  final String appId;


  static AppConfig? _instance;

  const AppConfig({
    required this.appName,
    required this.siteId,
    required this.appId,
  });

  /// Initializes the singleton instance with the provided configuration.
  static void initialize(AppConfig config) {
    _instance = config;
  }

  /// Provides access to the singleton instance.
  ///
  /// Throws an assertion error if accessed before initialization.
  static AppConfig get instance {
    assert(_instance != null, 'AppConfig must be initialized before accessing instance');
    return _instance!;
  }
  /// Returns all OSS URLs for the app configuration based on site ID and environment
  static List<String> getOssList({String? siteId}) {
    final regions = [
      {'prefix': 'rs-1337543130', 'region': 'ap-shanghai'},
      {'prefix': 'bj-1337543130', 'region': 'ap-beijing'},
      {'prefix': 'gz-1337543130', 'region': 'ap-guangzhou'},
      {'prefix': 'cq-1337543130', 'region': 'ap-chongqing'},
      {'prefix': 'xg-1337543130', 'region': 'ap-hongkong'},
    ];

    return regions.map((region) {
      final prefix = region['prefix'];
      final regionName = region['region'];
      return 'https://$prefix.cos.$regionName.myqcloud.com/${F.channel}/${siteId ?? instance.siteId}/app_api.json';
    }).toList();
  }
  /// Makes an HTTP request to the first OSS URL and returns the response with fastest API URL
  static Future<Map<String, dynamic>> fetchOssData({String? siteId}) async {
    final dio = Dio();
    final urls = getOssList(siteId: siteId);
    final url = urls.first; // Always use the first URL

    try {
      // Fetch data from the first URL
      final response = await dio.get(url);

      // Get the fastest API URL using the same URLs list
      final fastestApiUrl = await _getFastestApiUrlFromList(urls);

      return {
        'success': true,
        'url': url,
        'statusCode': response.statusCode,
        'data': response.data,
        'headers': response.headers.map,
        'fastestApiUrl': fastestApiUrl, // Include the fastest API URL in response
      };
    } catch (e) {
      // Even if main request fails, try to get the fastest API URL
      final fastestApiUrl = await _getFastestApiUrlFromList(urls);

      return {
        'success': false,
        'url': url,
        'error': e.toString(),
        'data': null,
        'fastestApiUrl': fastestApiUrl, // Include the fastest API URL even on failure
      };
    }
  }

  /// Internal method to find fastest API URL from a given list of URLs
  static Future<String?> _getFastestApiUrlFromList(List<String> urls) async {
    final dio = Dio();

    // Set a reasonable timeout for the requests
    dio.options.connectTimeout = const Duration(seconds: 5);
    dio.options.receiveTimeout = const Duration(seconds: 10);

    try {
      // Create a list of futures for concurrent requests
      final futures = urls.map((url) async {
        final stopwatch = Stopwatch()..start();
        try {
          final response = await dio.get(url);
          stopwatch.stop();

          if (response.statusCode == 200) {
            return {
              'url': url,
              'responseTime': stopwatch.elapsedMilliseconds,
              'success': true,
            };
          }
        } catch (e) {
          stopwatch.stop();
          return {
            'url': url,
            'responseTime': stopwatch.elapsedMilliseconds,
            'success': false,
            'error': e.toString(),
          };
        }
        return null;
      }).toList();

      // Wait for all requests to complete
      final results = await Future.wait(futures);

      // Filter successful results and find the fastest one
      final successfulResults = results
          .where((result) => result != null && result['success'] == true)
          .toList();

      if (successfulResults.isEmpty) {
        return null; // No successful requests
      }

      // Sort by response time and get the fastest
      successfulResults.sort((a, b) =>
          (a!['responseTime'] as int).compareTo(b!['responseTime'] as int));

      final fastestUrl = successfulResults.first!['url'] as String;

      // Extract the base domain and append "https://pc."
      final uri = Uri.parse(fastestUrl);
      final baseDomain = uri.host;

      return 'https://pc.$baseDomain';

    } catch (e) {
      return null;
    }
  }


}
